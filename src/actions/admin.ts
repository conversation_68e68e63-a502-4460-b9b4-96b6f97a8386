// 'use server';

import { TwilioPhoneNumbersResponse } from '@/lib/twilio-types';
import { API_ENDPOINTS, QueryParams } from '.';
import {
  ImportPhoneNumberPayload,
  UpdatePhoneNumberPayload,
} from '@/lib/admin-types';
import { ApiData, ClinicType, StaffMembersResponse } from '@/lib/types';

export interface IUser {
  _id: string;
  email: string;
  mobile?: string;
  country_code?: string;
  first_name?: string;
  last_name?: string;
  uid: string; // cognito username
  created_at: Date;
  updated_at: Date;
  last_login?: Date;
}

export enum AdminPrivilegeLevel {
  VIEWER = 'viewer',
  EDITOR = 'editor',
  OWNER = 'owner',
}

interface ApiResponseUsers {
  data: IUser[];
}
interface ApiResponse {
  data: ApiData;
  error?: string;
}

export interface AdminUser extends IUser {
  admin_level: AdminPrivilegeLevel;
}

export async function getAdminClinics(token: string, idToken: string) {
  try {
    const response = await fetch(API_ENDPOINTS.ADMIN_CLINICS, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: token ? `Bearer ${token}` : '',
        'x-id-token': idToken,
      },
      next: {
        revalidate: 6000,
        tags: ['clinics'],
      },
    });

    const data = await response.json();

    return {
      ok: response.ok,
      status: response.status,
      data,
    };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to fetch clinics. Please try again.',
    };
  }
}

export async function getClinicById(token?: string, clinicId?: string) {
  if (!clinicId) {
    return {
      ok: false,
      status: 400,
      error: 'Clinic ID is required',
    };
  }

  try {
    const url = API_ENDPOINTS.CLINIC_DETAILS.replace(':clinic_id', clinicId);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: token ? `Bearer ${token}` : '',
      },
      next: {
        revalidate: 6000,
        tags: [`clinic-${clinicId}`],
      },
    });

    const data = await response.json();

    return {
      ok: response.ok,
      status: response.status,
      data,
    };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to fetch clinic details. Please try again.',
    };
  }
}

export async function getClinicAnalytics(token: string, clinicId: string) {
  if (!clinicId) {
    return {
      ok: false,
      status: 400,
      error: 'Clinic ID is required',
    };
  }

  try {
    const url = new URL(API_ENDPOINTS.RETELL_ANALYTICS_LIST_CALLS);
    url.searchParams.set(QueryParams.CLINIC_ID, clinicId);

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      next: {
        revalidate: 6000,
        tags: [`clinic-analytics-${clinicId}`],
      },
    });

    const data = await response.json();

    return {
      ok: response.ok,
      status: response.status,
      data,
    };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to fetch clinic analytics. Please try again.',
    };
  }
}

export async function getTwilioPhoneNumbers(token: string, idToken: string) {
  try {
    const response = await fetch(API_ENDPOINTS.ADMIN_TWILIO_PHONE_NUMBERS, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
        'x-id-token': idToken,
      },
      cache: 'no-store',
    });

    const data = (await response.json()) as TwilioPhoneNumbersResponse;

    return {
      ok: response.ok,
      status: response.status,
      data,
    };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to fetch Twilio phone numbers. Please try again.',
    };
  }
}

export async function getAllUsers(
  token: string,
  idToken: string,
): Promise<{
  ok: boolean;
  status: number;
  data?: ApiResponseUsers;
  error?: string;
}> {
  try {
    const response = await fetch(API_ENDPOINTS.ADMIN_USERS, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
        'x-id-token': idToken,
      },
      cache: 'no-store',
    });

    const data: ApiResponseUsers = await response.json();
    return {
      ok: response.ok,
      status: response.status,
      data,
    };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to fetch the users. Please try again',
    };
  }
}

export async function getAllAdmins(
  token: string,
  idToken: string,
): Promise<{
  ok: boolean;
  status: number;
  data?: AdminUser[];
  error?: string;
}> {
  try {
    const response = await fetch(API_ENDPOINTS.ADMIN_ADMINS, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
        'x-id-token': idToken,
      },
      cache: 'no-store',
    });

    const data: {
      data: AdminUser[];
    } = await response.json();
    if (!response.ok) {
      return {
        ok: false,
        status: response.status,
        error: 'Failed to fetch admins.',
      };
    }
    return {
      ok: response.ok,
      status: response.status,
      data: data.data,
    };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to fetch the admins. Please try again',
    };
  }
}

export async function assignTwilioPhoneNumberToClinic(
  token: string,
  idToken: string,
  payload: ImportPhoneNumberPayload,
) {
  try {
    const response = await fetch(API_ENDPOINTS.ADMIN_ASSIGN_TWILIO_NUMBER, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
        'x-id-token': idToken,
      },
      body: JSON.stringify(payload),
    });

    const data = await response.json();
    return {
      ok: response.ok,
      status: response.status,
      data,
    };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to assign Twilio phone number. Please try again.',
    };
  }
}

export async function createOrUpdateClinicForAdmin(
  clinicData: Partial<ClinicType>,
  accessToken: string,
  idToken: string,
): Promise<{
  ok: boolean;
  status: number;
  data?: ClinicType;
  error?: string;
}> {
  try {
    const response = await fetch(API_ENDPOINTS.ADMIN_CLINIC, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
        'x-id-token': idToken,
      },
      body: JSON.stringify(clinicData),
    });

    const result = await response.json();

    return {
      ok: response.ok,
      status: response.status,
      data: result.data,
      error: !response.ok ? result?.message : undefined,
    };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to create or update clinic',
    };
  }
}

export async function registerAdmin(
  adminData: Record<string, string>,
  accessToken: string,
  idToken: string,
): Promise<{
  ok: boolean;
  status: number;
  data?: object;
  error?: string;
}> {
  try {
    const response = await fetch(API_ENDPOINTS.ADMIN_REGISTER_ADMIN, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
        'x-id-token': idToken,
      },
      body: JSON.stringify(adminData),
    });

    const result = await response.json();

    return {
      ok: response.ok,
      status: response.status,
      data: result.data,
      error: !response.ok ? result?.message : undefined,
    };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to register admin',
    };
  }
}

export async function removeAdmin(
  uid: string,
  accessToken: string,
  idToken: string,
): Promise<{
  ok: boolean;
  status: number;
  data?: object;
  error?: string;
}> {
  try {
    const response = await fetch(API_ENDPOINTS.ADMIN_REMOVE_ADMIN, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
        'x-id-token': idToken,
      },
      body: JSON.stringify({ uid }),
    });

    const result = await response.json();

    return {
      ok: response.ok,
      status: response.status,
      data: result.data,
      error: !response.ok ? result?.message : undefined,
    };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to remove admin',
    };
  }
}

export async function updateTwilioPhoneNumber(
  payload: UpdatePhoneNumberPayload,
  accessToken: string,
  idToken: string,
): Promise<{
  ok: boolean;
  status: number;
  data?: object;
  error?: string;
}> {
  try {
    const response = await fetch(API_ENDPOINTS.ADMIN_UPDATE_PHONE_NUMBER, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
        'x-id-token': idToken,
      },
      body: JSON.stringify(payload),
    });

    const result = await response.json();

    return {
      ok: response.ok,
      status: response.status,
      data: result.data,
      error: !response.ok ? result?.message : undefined,
    };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to update phone number',
    };
  }
}
export async function getAdminClinicOwners(
  auth: {
    accessToken: string | null;
    idToken: string | null;
  },
  clinicId: string,
): Promise<{
  ok: boolean;
  status: number;
  data?: StaffMembersResponse;
  error?: string;
}> {
  const { accessToken, idToken } = auth;
  try {
    const url = `${API_ENDPOINTS.ADMIN_CLINIC_OWNERS}?clinic_id=${clinicId}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
        'x-id-token': idToken ?? '',
      },
    });

    const result = await response.json();

    return {
      ok: response.ok,
      status: response.status,
      data: result,
      error: !response.ok ? result?.message : undefined,
    };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to fetch clinic owners',
    };
  }
}

export async function fetchAdminAnalyticsData(
  token: string,
  idToken: string,
  clinicId: string,
  startDate: string,
  endDate: string,
): Promise<{
  ok: boolean;
  status: number;
  data?: ApiData;
  error?: string;
}> {
  try {
    if (!token) {
      return {
        ok: false,
        status: 401,
        error: 'No access token provided',
      };
    }
    const url = `${API_ENDPOINTS.ADMIN_ANALYTICS_LIST_CALLS}?clinic_id=${clinicId}&start_date=${startDate}&end_date=${endDate}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: token ? `Bearer ${token}` : '',
        'x-id-token': idToken,
      },
      next: {
        revalidate: 6000,
        tags: ['analytics-data'],
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        ok: false,
        status: response.status,
        error: errorData.error.message || 'Failed to fetch analytics data',
      };
    }

    const result: ApiResponse = await response.json();
    return {
      ok: true,
      status: response.status,
      data: result.data,
    };
  } catch (error) {
    console.error('Error fetching analytics data:', error);
    return {
      ok: false,
      status: 500,
      error: 'Network error occurred while fetching analytics data',
    };
  }
}
