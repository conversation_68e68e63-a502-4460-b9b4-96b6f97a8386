'use client';

import { useState, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { AlertCircle } from 'lucide-react';
import { toast } from 'sonner';

import { useAdminClinic } from '@/contexts/AdminClinicContext';
import { createOrUpdateClinicForAdmin } from '@/actions/admin';
import { useAuth } from '@/contexts/AuthContext';
import { ClinicProfileForm } from '@/components/clinic';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import KnowledgeBasePage from '@/components/admin/KnowledgeBasePage';
import VoiceAgentPage from '@/components/admin/VoiceAgentPage';
import CRMDetailsPage from '@/components/admin/CRMDetailsPage';
import { ClinicFormValues } from '@/lib/clinic-form-schema';

const clinicFormSchema = z.object({
  clinic_name: z.string().min(2, {
    message: 'Clinic name must be at least 2 characters.',
  }),
  clinic_email: z.string().email({
    message: 'Please enter a valid email address.',
  }),
  clinic_website: z
    .string()
    .url({
      message: 'Please enter a valid URL.',
    })
    .optional()
    .or(z.literal('')),
  clinic_phone: z.string().optional(),
  human_transfer_destination_number: z.string().optional(),
  clinic_addresses: z
    .array(
      z.object({
        address: z.string().optional(),
        full_address: z.string().min(1, 'Full address is required'),
        business_location_id: z.string().optional(),
      }),
    )
    .optional(),
});

export default function ClinicProfilePage() {
  const { selectedClinic, setSelectedClinic } = useAdminClinic();
  // const [loading, setLoading] = useState(false);
  const [, setError] = useState<string | null>(null);

  const { getTokens } = useAuth();

  const form = useForm<z.infer<typeof clinicFormSchema>>({
    resolver: zodResolver(clinicFormSchema),
    defaultValues: {
      clinic_name: '',
      clinic_email: '',
      clinic_website: '',
      clinic_phone: '',
      human_transfer_destination_number: '',
      clinic_addresses: [],
    },
  });

  useEffect(() => {
    const handleClinicChanged = (event: CustomEvent) => {
      const clinic = event.detail;
      if (clinic) {
        setSelectedClinic(clinic);
        setError(null);
      }
    };

    window.addEventListener(
      'clinicChanged',
      handleClinicChanged as EventListener,
    );
    return () => {
      window.removeEventListener(
        'clinicChanged',
        handleClinicChanged as EventListener,
      );
    };
  }, [setSelectedClinic]);
  useEffect(() => {
    if (selectedClinic) {
      const addresses =
        selectedClinic.clinic_addresses?.map((addr) => ({
          address: typeof addr === 'string' ? addr : addr.address || '',
          full_address:
            typeof addr === 'string'
              ? addr
              : addr.full_address || addr.address || '',
          business_location_id:
            typeof addr === 'string' ? '' : addr.business_location_id || '',
        })) || [];

      form.reset({
        clinic_name: selectedClinic.clinic_name,
        clinic_email: selectedClinic.clinic_email,
        clinic_website: selectedClinic.clinic_website,
        clinic_phone: selectedClinic.clinic_phone || '',
        human_transfer_destination_number:
          selectedClinic.human_transfer_destination_number || '',
        clinic_addresses: addresses,
      });

      setError(null);
    }
  }, [selectedClinic, form]);

  // const onSubmit = async (values: z.infer<typeof clinicFormSchema>) => {
  //   if (!selectedClinic) {
  //     toast.error('No clinic selected');
  //     return;
  //   }

  //   setLoading(true);
  //   setError(null);

  //   try {
  //     const { accessToken, idToken } = await getTokens();

  //     if (!accessToken || !idToken) {
  //       toast.error('Authentication required');
  //       return;
  //     }

  //     const result = await createOrUpdateClinicForAdmin(
  //       { ...values, _id: selectedClinic._id },
  //       accessToken,
  //       idToken,
  //     );

  //     if (result.ok) {
  //       toast.success('Clinic updated successfully');
  //       if (result.data) {
  //         setSelectedClinic(result.data);
  //       }
  //     } else {
  //       setError(result.error || 'Failed to update clinic');
  //       toast.error(result.error || 'Failed to update clinic');
  //     }
  //   } catch (error) {
  //     console.error('Error updating clinic:', error);
  //     setError('An unexpected error occurred');
  //     toast.error('An unexpected error occurred');
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  // const handleClinicUpdate = (updatedClinic: ClinicType) => {
  //   setSelectedClinic(updatedClinic);
  // };

  const handleClinicFormSubmit = async (values: ClinicFormValues) => {
    try {
      const { accessToken, idToken } = await getTokens();

      if (!accessToken || !selectedClinic || !idToken) {
        toast.error('Authentication failed');
        return;
      }

      const result = await createOrUpdateClinicForAdmin(
        { ...values, _id: selectedClinic._id },
        accessToken,
        idToken,
      );

      if (result.ok && result.data) {
        toast.success('Clinic updated successfully');
        setSelectedClinic(result.data);
      } else {
        toast.error(result.error || 'Update failed');
      }
    } catch (err) {
      console.error(err);
      toast.error('An unexpected error occurred');
    }
  };

  if (!selectedClinic) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
          <p className="text-muted-foreground">No clinic selected</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Clinic Profile</h1>
        <p className="text-muted-foreground">
          Manage your clinic information and settings
        </p>
      </div>

      <Tabs defaultValue="basic-details">
        <TabsList>
          <TabsTrigger value="basic-details">Basic Details</TabsTrigger>
          <TabsTrigger value="crm-details">CRM Details</TabsTrigger>
          <TabsTrigger value="agent-details">Agent Details</TabsTrigger>
          <TabsTrigger value="knowledge-base">
            Knowledge Base Details
          </TabsTrigger>
        </TabsList>
        <TabsContent value="basic-details">
          <ClinicProfileForm
            selectedClinic={selectedClinic}
            onSubmitHandler={handleClinicFormSubmit}
          />
        </TabsContent>
        <TabsContent value="agent-details">
          <VoiceAgentPage></VoiceAgentPage>
        </TabsContent>
        <TabsContent value="knowledge-base">
          <KnowledgeBasePage></KnowledgeBasePage>
        </TabsContent>
        <TabsContent value="crm-details">
          <CRMDetailsPage></CRMDetailsPage>
        </TabsContent>
      </Tabs>
    </div>
  );
}
